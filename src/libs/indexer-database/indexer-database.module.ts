import { DrizzlePGModule } from '@knaadh/nestjs-drizzle-pg'
import { Module } from '@nestjs/common'
import { ConfigModule, ConfigService } from '@nestjs/config'

import * as schema from '../indexer/schema'

@Module({
  imports: [
    DrizzlePGModule.registerAsync({
      tag: 'INDEXER_DB',
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const indexerDatabaseUrl = configService.get<string>('INDEXER_DATABASE_URL')

        return {
          pg: {
            connection: 'client',
            config: {
              connectionString: indexerDatabaseUrl,
              ssl: {
                rejectUnauthorized: false,
              },
              // Read-only configuration
              options: '-c default_transaction_read_only=on',
            },
          },
          config: {
            schema: { ...schema },
            logger: process.env.NODE_ENV !== 'production',
          },
        }
      },
    }),
  ],
  exports: [DrizzlePGModule],
})
export class IndexerDatabaseModule {}
