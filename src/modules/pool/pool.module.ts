import { Module } from '@nestjs/common'

import { PoolController } from './pool.controller'
import { PoolService } from './pool.service'
import { DatabaseModule } from '../../libs/database/database.module'
import { IndexerDatabaseModule } from '../../libs/indexer-database/indexer-database.module'
import { BlockchainModule } from '../blockchain/blockchain.module'

@Module({
  imports: [BlockchainModule, DatabaseModule, IndexerDatabaseModule],
  controllers: [PoolController],
  providers: [PoolService],
  exports: [PoolService],
})
export class PoolModule {}
