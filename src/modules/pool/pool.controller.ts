import { Controller, Post, UseGuards, Body } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiHeader, ApiSecurity } from '@nestjs/swagger'

import { CheckLiquidityDto, CheckLiquidityResponseDto } from './dto/check-liquidity.dto'
import { CreatePoolDto } from './dto/create-pool.dto'
import { PoolService } from './pool.service'
import { AdminKeyGuard } from '../auth/guards/admin-key.guard'

@ApiTags('Pool Admin')
@Controller('pool')
@ApiSecurity('AdminKey')
export class PoolController {
  constructor(private readonly poolService: PoolService) {}

  @Post('create')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiOperation({
    summary: 'Create a liquidity pool (Admin only)',
    description: `
    Creates a new liquidity pool on the specified blockchain for a project.
    
    **Requirements:**
    - Project must exist in the database
    - Project must not already have a pool address
    - Valid blockchain and token configuration
    
    **Supported Chains:**
    - mainnet: Ethereum mainnet (Uniswap V3)
    - arbitrum: Arbitrum (Uniswap V3)
    - base: Base (Uniswap V3)
    - solana: Solana (Raydium/Orca)
    
    **Pool Creation Process:**
    1. Validates project exists and has no existing pool
    2. Validates token addresses/mints
    3. Checks if pool already exists on blockchain
    4. Creates new pool contract/account
    5. Updates project with pool address
    6. Returns pool address and transaction hash
    
    **Admin Key:** Required in X-Admin-Key header
    `,
  })
  @ApiResponse({
    status: 201,
    description: 'Pool created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        chain: { type: 'string', example: 'mainnet' },
        poolAddress: { type: 'string', example: '******************************************' },
        txHash: { type: 'string', example: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890' },
        projectId: { type: 'string', example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid pool configuration or project already has pool',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        chain: { type: 'string', example: 'mainnet' },
        error: { type: 'string', example: 'Project already has a pool address' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Project not found',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Project with ID f47ac10b-58cc-4372-a567-0e02b2c3d479 not found' },
        error: { type: 'string', example: 'Not Found' },
        statusCode: { type: 'number', example: 404 },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing admin key',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Admin key is required. Please provide X-Admin-Key header.' },
        error: { type: 'string', example: 'Unauthorized' },
        statusCode: { type: 'number', example: 401 },
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin access not configured',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Admin access is not configured on this server' },
        error: { type: 'string', example: 'Forbidden' },
        statusCode: { type: 'number', example: 403 },
      },
    },
  })
  async createPool(@Body() createPoolDto: CreatePoolDto) {
    const result = await this.poolService.createPool(createPoolDto)

    return {
      ...result,
      ...createPoolDto,
    }
  }

  @Post('check-liquidity')
  @UseGuards(AdminKeyGuard)
  @ApiHeader({
    name: 'X-Admin-Key',
    description: 'Admin authentication key',
    required: true,
    example: 'your-secret-admin-key-here',
  })
  @ApiOperation({
    summary: 'Check if liquidity has been added by project manager (Admin only)',
    description: `
    Checks if the project manager has added liquidity to the pool for a given project.

    **Requirements:**
    - Project must exist in the database
    - Project must have a pool address

    **Process:**
    1. Validates project exists and has a pool address
    2. Queries the indexer database to check for manager liquidity additions
    3. If liquidity is confirmed, updates project status to 'liquidity_provided'
    4. Returns liquidity status and project information

    **Response includes:**
    - Whether liquidity has been added by the manager
    - Pool address and project manager address
    - Current project status (updated if liquidity confirmed)
    `,
  })
  @ApiResponse({
    status: 200,
    description: 'Liquidity check completed successfully',
    type: CheckLiquidityResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request or project not found',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        hasLiquidity: { type: 'boolean', example: false },
        error: { type: 'string', example: 'Project not found' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing admin key',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Admin key is required. Please provide X-Admin-Key header.' },
        error: { type: 'string', example: 'Unauthorized' },
        statusCode: { type: 'number', example: 401 },
      },
    },
  })
  async checkLiquidity(@Body() checkLiquidityDto: CheckLiquidityDto): Promise<CheckLiquidityResponseDto> {
    return this.poolService.checkLiquidity(checkLiquidityDto.projectId)
  }
}
